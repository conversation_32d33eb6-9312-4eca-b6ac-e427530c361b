model CarListingFeature {
    id        BigInt   @id @default(autoincrement())
    listingId BigInt   @map("listing_id")
    featureId BigInt   @map("feature_id")
    createdAt DateTime @default(now()) @map("created_at")

    listing CarListing @relation(fields: [listingId], references: [id], onDelete: Cascade)
    feature CarFeature @relation(fields: [featureId], references: [id], onDelete: Cascade)

    @@unique([listingId, featureId])
    @@index([listingId])
    @@index([featureId])
    @@map("car_listing_features")
}
