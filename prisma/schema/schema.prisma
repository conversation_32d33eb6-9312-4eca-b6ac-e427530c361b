generator client {
  provider        = "prisma-client-js"
  previewFeatures = ["prismaSchemaFolder"]
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

enum Role {
  BUYER  @map("buyer")
  SELLER @map("seller")
  ADMIN  @map("admin")
}

enum Status {
  ACTIVE    @map("active")
  INACTIVE  @map("inactive")
  SUSPENDED @map("suspended")
}

enum CarCondition {
  NEW       @map("new")
  USED      @map("used")
  CERTIFIED @map("certified")
}

enum FuelType {
  GASOLINE @map("gasoline")
  DIESEL   @map("diesel")
  ELECTRIC @map("electric")
  HYBRID   @map("hybrid")
}

enum TransmissionType {
  MANUAL         @map("manual")
  AUTOMATIC      @map("automatic")
  SEMI_AUTOMATIC @map("semi-automatic")
}

enum DriveType {
  FWD    @map("fwd")
  RWD    @map("rwd")
  AWD    @map("awd")
  FOURWD @map("4wd")
}

enum ListingType {
  CAR       @map("car")
  ACCESSORY @map("accessory")
}

enum ListingStatus {
  ACTIVE  @map("active")
  PENDING @map("pending")
  SOLD    @map("sold")
  DRAFT   @map("draft")
}

enum PostStatus {
  DRAFT     @map("draft")
  PUBLISHED @map("published")
  ARCHIVED  @map("archived")
}

enum ProductStatus {
  ACTIVE   @map("active")
  INACTIVE @map("inactive")
  DRAFT    @map("draft")
}

enum StockStatus {
  INSTOCK     @map("instock")
  OUTOFSTOCK  @map("outofstock")
  ONBACKORDER @map("onbackorder")
}

enum OrderStatus {
  PENDING    @map("pending")
  PROCESSING @map("processing")
  SHIPPED    @map("shipped")
  DELIVERED  @map("delivered")
  CANCELLED  @map("cancelled")
}

enum PaymentStatus {
  PENDING  @map("pending")
  PAID     @map("paid")
  FAILED   @map("failed")
  REFUNDED @map("refunded")
}

enum BillingPeriod {
  MONTHLY @map("monthly")
  YEARLY  @map("yearly")
}

enum SubscriptionStatus {
  ACTIVE    @map("active")
  CANCELLED @map("cancelled")
  EXPIRED   @map("expired")
}

enum SettingType {
  STRING  @map("string")
  NUMBER  @map("number")
  BOOLEAN @map("boolean")
  JSON    @map("json")
}

enum ItemCondition {
  NEW         @map("new")
  USED        @map("used")
  REFURBISHED @map("refurbished")
}
