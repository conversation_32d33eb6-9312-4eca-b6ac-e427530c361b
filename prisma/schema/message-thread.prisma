model MessageThread {
    id            BigInt    @id @default(autoincrement())
    user1Id       BigInt    @map("user1_id")
    user2Id       BigInt    @map("user2_id")
    listingId     BigInt?   @map("listing_id")
    lastMessageId BigInt?   @map("last_message_id")
    lastMessageAt DateTime? @map("last_message_at")
    createdAt     DateTime  @default(now()) @map("created_at")

    user1       User        @relation("User1Threads", fields: [user1Id], references: [id], onDelete: Cascade)
    user2       User        @relation("User2Threads", fields: [user2Id], references: [id], onDelete: Cascade)
    listing     CarListing? @relation(fields: [listingId], references: [id], onDelete: SetNull)
    lastMessage Message?    @relation(fields: [lastMessageId], references: [id], onDelete: SetNull)

    @@unique([user1Id, user2Id, listingId])
    @@index([user1Id])
    @@index([user2Id])
    @@index([lastMessageAt])
    @@map("message_threads")
}
