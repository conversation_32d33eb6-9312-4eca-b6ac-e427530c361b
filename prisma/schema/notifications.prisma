model Notification {
    id        BigInt   @id @default(autoincrement())
    userId    BigInt   @map("user_id")
    type      String   @db.VarChar(50)
    title     String   @db.VarChar(255)
    message   String   @db.Text
    data      Json?
    isRead    Boolean  @default(false) @map("is_read")
    createdAt DateTime @default(now()) @map("created_at")

    user User @relation(fields: [userId], references: [id], onDelete: Cascade)

    @@index([userId])
    @@index([type])
    @@index([isRead])
    @@index([createdAt])
    @@map("notifications")
}
