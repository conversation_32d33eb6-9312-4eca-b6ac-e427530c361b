model Order {
    id              BigInt        @id @default(autoincrement())
    userId          BigInt?       @map("user_id")
    orderNumber     String        @unique @map("order_number") @db.VarChar(50)
    status          OrderStatus   @default(PENDING)
    subtotal        Decimal       @db.Decimal(10, 2)
    taxAmount       Decimal       @default(0) @map("tax_amount") @db.Decimal(10, 2)
    shippingAmount  Decimal       @default(0) @map("shipping_amount") @db.Decimal(10, 2)
    totalAmount     Decimal       @map("total_amount") @db.Decimal(10, 2)
    paymentStatus   PaymentStatus @default(PENDING) @map("payment_status")
    paymentMethod   String?       @map("payment_method") @db.VarChar(50)
    shippingAddress Json?         @map("shipping_address")
    billingAddress  Json?         @map("billing_address")
    notes           String?       @db.Text
    createdAt       DateTime      @default(now()) @map("created_at")
    updatedAt       DateTime      @updatedAt @map("updated_at")

    user  User?       @relation(fields: [userId], references: [id], onDelete: SetNull)
    items OrderItem[]

    @@index([userId])
    @@index([orderNumber])
    @@index([status])
    @@index([paymentStatus])
    @@map("orders")
}

model OrderItem {
    id        BigInt   @id @default(autoincrement())
    orderId   BigInt   @map("order_id")
    productId BigInt   @map("product_id")
    quantity  Int
    price     Decimal  @db.Decimal(10, 2)
    total     Decimal  @db.Decimal(10, 2)
    createdAt DateTime @default(now()) @map("created_at")

    order   Order   @relation(fields: [orderId], references: [id], onDelete: Cascade)
    product Product @relation(fields: [productId], references: [id], onDelete: Cascade)

    @@index([orderId])
    @@index([productId])
    @@map("order_items")
}
