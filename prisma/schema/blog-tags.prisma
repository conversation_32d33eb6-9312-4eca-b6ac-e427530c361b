model BlogTag {
    id        BigInt   @id @default(autoincrement())
    name      String   @unique @db.VarChar(50)
    slug      String   @unique @db.VarChar(50)
    createdAt DateTime @default(now()) @map("created_at")

    posts BlogPostTag[]

    @@index([slug])
    @@map("blog_tags")
}

model BlogPostTag {
    id        BigInt   @id @default(autoincrement())
    postId    BigInt   @map("post_id")
    tagId     BigInt   @map("tag_id")
    createdAt DateTime @default(now()) @map("created_at")

    post BlogPost @relation(fields: [postId], references: [id], onDelete: Cascade)
    tag  BlogTag  @relation(fields: [tagId], references: [id], onDelete: Cascade)

    @@unique([postId, tagId])
    @@index([postId])
    @@index([tagId])
    @@map("blog_post_tags")
}
