model ShoppingCart {
  id        BigInt   @id @default(autoincrement())
  userId    BigInt?  @map("user_id")
  sessionId String?  @map("session_id") @db.VarChar(255)
  productId BigInt   @map("product_id")
  quantity  Int      @default(1)
  createdAt DateTime @default(now()) @map("created_at")
  updatedAt DateTime @updatedAt @map("updated_at")

  user    User?   @relation(fields: [userId], references: [id], onDelete: Cascade)
  product Product @relation(fields: [productId], references: [id], onDelete: Cascade)

  @@index([userId])
  @@index([sessionId])
  @@index([productId])
  @@map("shopping_cart")
}