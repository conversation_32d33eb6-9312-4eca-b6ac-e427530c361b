model ContactForm {
    id        BigInt   @id @default(autoincrement())
    name      String   @db.VarChar(100)
    email     String   @db.VarChar(255)
    phone     String?  @db.VarChar(20)
    subject   String?  @db.VarChar(255)
    message   String   @db.Text
    listingId BigInt?  @map("listing_id")
    isRead    Boolean  @default(false) @map("is_read")
    createdAt DateTime @default(now()) @map("created_at")

    listing CarListing? @relation(fields: [listingId], references: [id], onDelete: SetNull)

    @@index([email])
    @@index([listingId])
    @@index([isRead])
    @@index([createdAt])
    @@map("contact_forms")
}
