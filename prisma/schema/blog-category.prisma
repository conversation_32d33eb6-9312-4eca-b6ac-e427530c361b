model BlogCategory {
    id          BigInt   @id @default(autoincrement())
    name        String   @unique @db.VarChar(100)
    slug        String   @unique @db.VarChar(100)
    description String?  @db.Text
    isActive    Boolean  @default(true) @map("is_active")
    createdAt   DateTime @default(now()) @map("created_at")

    posts BlogPost[]

    @@index([slug])
    @@index([isActive])
    @@map("blog_categories")
}
