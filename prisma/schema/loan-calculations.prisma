model LoanCalculation {
    id             BigInt   @id @default(autoincrement())
    userId         BigInt?  @map("user_id")
    vehiclePrice   Decimal  @map("vehicle_price") @db.Decimal(12, 2)
    downPayment    Decimal  @map("down_payment") @db.Decimal(12, 2)
    interestRate   Decimal  @map("interest_rate") @db.Decimal(5, 2)
    loanTermMonths Int      @map("loan_term_months")
    monthlyPayment Decimal  @map("monthly_payment") @db.Decimal(10, 2)
    totalInterest  Decimal  @map("total_interest") @db.Decimal(12, 2)
    totalAmount    Decimal  @map("total_amount") @db.Decimal(12, 2)
    createdAt      DateTime @default(now()) @map("created_at")

    user User? @relation(fields: [userId], references: [id], onDelete: SetNull)

    @@index([userId])
    @@index([createdAt])
    @@map("loan_calculations")
}
