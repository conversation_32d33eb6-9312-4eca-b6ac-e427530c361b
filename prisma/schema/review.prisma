model Review {
  id           BigInt   @id @default(autoincrement())
  userId       BigInt   @map("user_id")
  listingId    BigInt?  @map("listing_id")
  productId    BigInt?  @map("product_id")
  rating       Int
  title        String?  @db.VarChar(255)
  content      String?  @db.Text
  isApproved   Boolean  @default(false) @map("is_approved")
  helpfulCount Int      @default(0) @map("helpful_count")
  createdAt    DateTime @default(now()) @map("created_at")
  updatedAt    DateTime @updatedAt @map("updated_at")

  user    User        @relation(fields: [userId], references: [id], onDelete: Cascade)
  listing CarListing? @relation(fields: [listingId], references: [id], onDelete: Cascade)
  product Product?    @relation(fields: [productId], references: [id], onDelete: Cascade)

  @@index([userId])
  @@index([listingId])
  @@index([productId])
  @@index([rating])
  @@index([isApproved])
  @@map("reviews")
}