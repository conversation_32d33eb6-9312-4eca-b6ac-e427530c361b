model UserView {
    id        BigInt   @id @default(autoincrement())
    userId    BigInt?  @map("user_id")
    listingId BigInt   @map("listing_id")
    ipAddress String?  @map("ip_address") @db.VarChar(45)
    userAgent String?  @map("user_agent") @db.Text
    viewedAt  DateTime @default(now()) @map("viewed_at")

    user    User?      @relation(fields: [userId], references: [id], onDelete: SetNull)
    listing CarListing @relation(fields: [listingId], references: [id], onDelete: Cascade)

    @@index([userId])
    @@index([listingId])
    @@index([viewedAt])
    @@map("user_views")
}
