model UserSubscription {
    id        BigInt             @id @default(autoincrement())
    userId    BigInt             @map("user_id")
    planId    BigInt             @map("plan_id")
    status    SubscriptionStatus @default(ACTIVE)
    startDate DateTime           @map("start_date")
    endDate   DateTime           @map("end_date")
    autoRenew Boolean            @default(true) @map("auto_renew")
    createdAt DateTime           @default(now()) @map("created_at")

    user User        @relation(fields: [userId], references: [id], onDelete: Cascade)
    plan PricingPlan @relation(fields: [planId], references: [id], onDelete: Cascade)

    @@index([userId])
    @@index([planId])
    @@index([status])
    @@index([endDate])
    @@map("user_subscriptions")
}
