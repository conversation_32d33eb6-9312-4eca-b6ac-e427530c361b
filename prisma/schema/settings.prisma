model SystemSetting {
    id           BigInt      @id @default(autoincrement())
    settingKey   String      @unique @map("setting_key") @db.VarChar(100)
    settingValue String?     @map("setting_value") @db.Text
    settingType  SettingType @default(STRING) @map("setting_type")
    description  String?     @db.Text
    isPublic     Boolean     @default(false) @map("is_public")
    createdAt    DateTime    @default(now()) @map("created_at")
    updatedAt    DateTime    @updatedAt @map("updated_at")

    @@index([settingKey])
    @@index([isPublic])
    @@map("system_settings")
}
