model Product {
    id               BigInt        @id @default(autoincrement())
    name             String        @db.Var<PERSON>har(255)
    slug             String        @unique @db.VarChar(255)
    description      String?       @db.Text
    shortDescription String?       @map("short_description") @db.Text
    price            Decimal       @db.Decimal(10, 2)
    salePrice        Decimal?      @map("sale_price") @db.Decimal(10, 2)
    sku              String?       @unique @db.VarChar(100)
    stockQuantity    Int           @default(0) @map("stock_quantity")
    manageStock      Boolean       @default(true) @map("manage_stock")
    stockStatus      StockStatus   @default(INSTOCK) @map("stock_status")
    weight           Decimal?      @db.Decimal(8, 2)
    dimensions       String?       @db.VarChar(100)
    featuredImage    String?       @map("featured_image") @db.VarChar(500)
    galleryImages    Json?         @map("gallery_images")
    status           ProductStatus @default(ACTIVE)
    createdAt        DateTime      @default(now()) @map("created_at")
    updatedAt        DateTime      @updatedAt @map("updated_at")

    cartItems  ShoppingCart[]
    orderItems OrderItem[]
    reviews    Review[]

    @@index([slug])
    @@index([sku])
    @@index([status])
    @@index([stockStatus])
    @@map("products")
}
