model PricingPlan {
    id            BigInt        @id @default(autoincrement())
    name          String        @db.VarChar(100)
    description   String?       @db.Text
    price         Decimal       @db.Decimal(10, 2)
    billingPeriod BillingPeriod @map("billing_period")
    features      Json?
    maxListings   Int?          @map("max_listings")
    isFeatured    Boolean       @default(false) @map("is_featured")
    isActive      Boolean       @default(true) @map("is_active")
    createdAt     DateTime      @default(now()) @map("created_at")

    subscriptions UserSubscription[]

    @@index([isActive])
    @@index([isFeatured])
    @@map("pricing_plans")
}
