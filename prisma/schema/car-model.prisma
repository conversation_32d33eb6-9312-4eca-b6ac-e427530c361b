model CarModel {
    id        BigInt   @id @default(autoincrement())
    makeId    BigInt   @map("make_id")
    name      String   @db.VarChar(100)
    yearStart Int?     @map("year_start")
    yearEnd   Int?     @map("year_end")
    isActive  Boolean  @default(true) @map("is_active")
    createdAt DateTime @default(now()) @map("created_at")

    make     CarMake      @relation(fields: [makeId], references: [id], onDelete: Cascade)
    listings CarListing[]
    // accessoryCompatibility AccessoryCompatibility[]

    @@index([makeId])
    @@index([name])
    @@index([isActive])
    @@map("car_models")
}
