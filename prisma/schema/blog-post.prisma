model BlogPost {
    id              BigInt     @id @default(autoincrement())
    authorId        BigInt     @map("author_id")
    categoryId      BigInt?    @map("category_id")
    title           String     @db.VarChar(255)
    slug            String     @unique @db.VarChar(255)
    excerpt         String?    @db.VarChar(500)
    content         String     @db.VarChar(5000)
    featuredImage   String?    @map("featured_image") @db.VarChar(500)
    status          PostStatus @default(DRAFT)
    publishedAt     DateTime?  @map("published_at")
    viewsCount      Int        @default(0) @map("views_count")
    commentsCount   Int        @default(0) @map("comments_count")
    metaTitle       String?    @map("meta_title") @db.VarChar(255)
    metaDescription String?    @map("meta_description") @db.Text
    createdAt       DateTime   @default(now()) @map("created_at")
    updatedAt       DateTime   @updatedAt @map("updated_at")

    author   User          @relation(fields: [authorId], references: [id], onDelete: Cascade)
    category BlogCategory? @relation(fields: [categoryId], references: [id], onDelete: SetNull)
    tags     BlogPostTag[]
    comments BlogComment[]

    @@index([authorId])
    @@index([categoryId])
    @@index([slug])
    @@index([status])
    @@index([publishedAt])
    @@map("blog_posts")
}
