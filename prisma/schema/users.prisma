model User {
    id             BigInt    @id @default(autoincrement())
    email          String    @unique @db.VarChar(255)
    passwordHash   String    @map("password_hash") @db.Var<PERSON>har(255)
    firstName      String    @map("first_name") @db.VarChar(100)
    lastName       String    @map("last_name") @db.VarChar(100)
    phone          String?   @db.VarChar(20)
    profilePicture String?   @map("profile_picture") @db.VarChar(500)
    location       String?   @db.VarChar(255)
    bio            String?   @db.Text
    role           Role      @default(BUYER)
    status         Status    @default(ACTIVE)
    emailVerified  Boolean   @default(false) @map("email_verified")
    createdAt      DateTime  @default(now()) @map("created_at")
    updatedAt      DateTime  @updatedAt @map("updated_at")
    lastLogin      DateTime? @map("last_login")

    // Relations
    socialLinks      UserSocialLink[]
    carListings      CarListing[]
    // accessories      Accessory[]
    favorites        UserFavorite[]
    views            UserView[]
    sentMessages     Message[]          @relation("SentMessages")
    receivedMessages Message[]          @relation("ReceivedMessages")
    thread1          MessageThread[]    @relation("User1Threads")
    thread2          MessageThread[]    @relation("User2Threads")
    blogPosts        BlogPost[]
    blogComments     BlogComment[]
    cart             ShoppingCart[]
    orders           Order[]
    reviews          Review[]
    notifications    Notification[]
    subscriptions    UserSubscription[]
    searchHistory    SearchHistory[]
    loanCalculations LoanCalculation[]

    @@index([email])
    @@index([role])
    @@index([status])
    @@map("users")
}
