model CarListing {
    id             BigInt           @id @default(autoincrement())
    userId         BigInt           @map("user_id")
    title          String           @db.Var<PERSON>har(255)
    description    String?          @db.Text
    makeId         BigInt           @map("make_id")
    modelId        BigInt           @map("model_id")
    year           Int
    condition      CarCondition
    price          Decimal          @db.Decimal(12, 2)
    mileage        Int?
    fuelType       FuelType         @map("fuel_type")
    transmission   TransmissionType
    driveType      DriveType        @map("drive_type")
    engineSize     Decimal?         @map("engine_size") @db.Decimal(3, 1)
    cylinders      Int?
    doors          Int?
    seats          Int?
    color          String?          @db.VarChar(50)
    interiorColor  String?          @map("interior_color") @db.VarChar(50)
    vin            String?          @unique @db.VarChar(17)
    primaryImage   String?          @map("primary_image") @db.VarChar(500)
    galleryImages  Json?            @map("gallery_images")
    videos         Json?
    listingType    ListingType      @default(CAR) @map("listing_type")
    status         ListingStatus    @default(PENDING)
    featured       <PERSON><PERSON><PERSON>          @default(false)
    viewsCount     Int              @default(0) @map("views_count")
    favoritesCount Int              @default(0) @map("favorites_count")
    location       String?          @db.VarChar(255)
    latitude       Decimal?         @db.Decimal(10, 8)
    longitude      Decimal?         @db.Decimal(11, 8)
    contactPhone   String?          @map("contact_phone") @db.VarChar(20)
    contactEmail   String?          @map("contact_email") @db.VarChar(255)
    createdAt      DateTime         @default(now()) @map("created_at")
    updatedAt      DateTime         @updatedAt @map("updated_at")

    user           User                @relation(fields: [userId], references: [id], onDelete: Cascade)
    make           CarMake             @relation(fields: [makeId], references: [id])
    model          CarModel            @relation(fields: [modelId], references: [id])
    features       CarListingFeature[]
    favorites      UserFavorite[]
    views          UserView[]
    messages       Message[]
    messageThreads MessageThread[]
    reviews        Review[]
    // contactForms   ContactForm[]
    ContactForm    ContactForm[]

    @@index([userId])
    @@index([makeId, modelId])
    @@index([price])
    @@index([year])
    @@index([status])
    @@index([featured])
    @@index([listingType])
    @@index([latitude, longitude])
    @@map("car_listings")
}
