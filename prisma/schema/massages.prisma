model Message {
  id          BigInt   @id @default(autoincrement())
  senderId    BigInt   @map("sender_id")
  recipientId BigInt   @map("recipient_id")
  listingId   BigInt?  @map("listing_id")
  subject     String?  @db.VarChar(255)
  message     String   @db.Text
  isRead      Bo<PERSON>an  @default(false) @map("is_read")
  createdAt   DateTime @default(now()) @map("created_at")

  sender       User           @relation("SentMessages", fields: [senderId], references: [id], onDelete: Cascade)
  recipient    User           @relation("ReceivedMessages", fields: [recipientId], references: [id], onDelete: Cascade)
  listing      CarListing?    @relation(fields: [listingId], references: [id], onDelete: SetNull)
  lastThreads  MessageThread[]

  @@index([senderId])
  @@index([recipientId])
  @@index([listingId])
  @@index([isRead])
  @@index([createdAt])
  @@map("messages")
}