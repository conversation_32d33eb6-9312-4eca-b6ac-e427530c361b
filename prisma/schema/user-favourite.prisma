model UserFavorite {
    id        BigInt   @id @default(autoincrement())
    userId    BigInt   @map("user_id")
    listingId BigInt   @map("listing_id")
    createdAt DateTime @default(now()) @map("created_at")

    user    User       @relation(fields: [userId], references: [id], onDelete: Cascade)
    listing CarListing @relation(fields: [listingId], references: [id], onDelete: Cascade)

    @@unique([userId, listingId])
    @@index([userId])
    @@index([listingId])
    @@map("user_favorites")
}
