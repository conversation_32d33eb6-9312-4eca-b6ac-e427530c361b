model CarMake {
    id        BigInt   @id @default(autoincrement())
    name      String   @unique @db.VarChar(100)
    logo      String?  @db.VarChar(500)
    isActive  Boolean  @default(true) @map("is_active")
    createdAt DateTime @default(now()) @map("created_at")

    models   CarModel[]
    listings CarListing[]
    // accessoryCompatibility AccessoryCompatibility[]

    @@index([name])
    @@index([isActive])
    @@map("car_makes")
}
