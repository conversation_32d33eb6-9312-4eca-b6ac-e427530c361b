model BlogComment {
    id         BigInt   @id @default(autoincrement())
    postId     BigInt   @map("post_id")
    userId     BigInt?  @map("user_id")
    parentId   BigInt?  @map("parent_id")
    name       String?  @db.VarChar(100)
    email      String?  @db.VarChar(255)
    content    String   @db.Text
    isApproved Boolean  @default(false) @map("is_approved")
    createdAt  DateTime @default(now()) @map("created_at")

    post    BlogPost      @relation(fields: [postId], references: [id], onDelete: Cascade)
    user    User?         @relation(fields: [userId], references: [id], onDelete: SetNull)
    parent  BlogComment?  @relation("CommentReplies", fields: [parentId], references: [id], onDelete: Cascade)
    replies BlogComment[] @relation("CommentReplies")

    @@index([postId])
    @@index([userId])
    @@index([parentId])
    @@index([isApproved])
    @@map("blog_comments")
}
