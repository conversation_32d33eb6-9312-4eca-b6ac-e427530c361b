model SearchHistory {
    id           BigInt   @id @default(autoincrement())
    userId       BigInt?  @map("user_id")
    searchQuery  String   @map("search_query") @db.VarChar(500)
    filters      Json?
    resultsCount Int?     @map("results_count")
    ipAddress    String?  @map("ip_address") @db.VarChar(45)
    createdAt    DateTime @default(now()) @map("created_at")

    user User? @relation(fields: [userId], references: [id], onDelete: SetNull)

    @@index([userId])
    @@index([createdAt])
    @@map("search_history")
}
